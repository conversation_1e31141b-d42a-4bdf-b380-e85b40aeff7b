{"name": "t4", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@vuepic/vue-datepicker": "^11.0.2", "axios": "^1.8.4", "html-to-image": "^1.11.13", "katex": "^0.16.22", "marked": "^15.0.8", "vis-network": "^9.1.9", "vue": "^3.2.13", "vue-router": "^4.0.3", "vue3-datepicker": "^0.4.0", "vuex": "^4.0.0"}, "devDependencies": {"@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}