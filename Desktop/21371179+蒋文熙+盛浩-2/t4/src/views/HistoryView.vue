<template>
    <div class="grid-container">
        <History v-for="(item, index) in items" :key="index" :date="item.date" :type="item.type"
            :para3="item.title" :id="item.id"/>
    </div>

</template>

<script>

import History from '@/components/History.vue';
import request from '@/utils/api';
export default {
    components: {
        // PaperPreview
        History
    },
    data() {
        return {
            date: '',
            items: [],

        }
    },
    methods: {

    },
    async mounted() {
        const res = await request.get('gethistory');
        this.items = res;
        console.log(res);

    }
}


</script>

<style scoped>
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    /* 响应式 */
    gap: 16px;
    padding: 16px;

    max-width: 1000px;
    margin: auto;
}
</style>