<template>
  <!-- <div style="align-items: center;"> -->
  <div class="generate-container">

    <GraphBox :graph="graph"/>
    <TextBox :data="content"/>

  </div>
  <!-- </div> -->
</template>


<script>

import GraphBox from '@/components/GraphBox.vue';
import TextBox from '@/components/TextBox.vue';
import request from '@/utils/api';
import { data } from 'vis-network';

export default {
  components: {
    GraphBox, TextBox
  },
  data() {
    return {
      content: '', 
      graph: null
    }
  },
  async mounted() {
    // console.log('组件已挂载');
    // 比如获取 DOM 元素
    // const el = this.$refs.myDiv;
    // 或者发送 Ajax 请求
    const res = await request.get(`getoutput?id=${this.$route.query.id}`);
    this.content = res['content'];
    this.graph = res['graph']
    // console.log(`this.graph: ${JSON.stringify(res['graph'], null, 2)}`);
    
  }
}
</script>

<style>
.generate-container {
  max-width: 1000px;
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  margin: auto;
}

@media (max-width: 768px) {
  .generate-container {
    flex-direction: column;
  }
}
</style>