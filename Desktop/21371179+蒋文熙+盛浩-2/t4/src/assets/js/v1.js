// utils.js - 通用工具函数，适用于 Vue 3 项目

export const isUndef = (v) => v === undefined || v === null;
export const isDef = (v) => v !== undefined && v !== null;
export const isTrue = (v) => v === true;
export const isFalse = (v) => v === false;

export const isPrimitive = (value) => (
  typeof value === 'string' ||
  typeof value === 'number' ||
  typeof value === 'symbol' ||
  typeof value === 'boolean'
);

export const isObject = (obj) => obj !== null && typeof obj === 'object';

const _toString = Object.prototype.toString;

export const toRawType = (value) => _toString.call(value).slice(8, -1);
export const isPlainObject = (obj) => _toString.call(obj) === '[object Object]';
export const isRegExp = (v) => _toString.call(v) === '[object RegExp]';

export const isPromise = (val) =>
  isDef(val) && typeof val.then === 'function' && typeof val.catch === 'function';

export const toString = (val) =>
  val == null
    ? ''
    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)
    ? JSON.stringify(val, null, 2)
    : String(val);

export const toNumber = (val) => {
  const n = parseFloat(val);
  return isNaN(n) ? val : n;
};

export const makeMap = (str, expectsLowerCase) => {
  const map = Object.create(null);
  const list = str.split(',');
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true;
  }
  return expectsLowerCase
    ? (val) => map[val.toLowerCase()]
    : (val) => map[val];
};

export const remove = (arr, item) => {
  if (arr.length) {
    const index = arr.indexOf(item);
    if (index > -1) {
      return arr.splice(index, 1);
    }
  }
};

export const hasOwn = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key);

export const cached = (fn) => {
  const cache = Object.create(null);
  return function cachedFn(str) {
    const hit = cache[str];
    return hit || (cache[str] = fn(str));
  };
};

const camelizeRE = /-(\w)/g;
export const camelize = cached((str) =>
  str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''))
);

export const capitalize = cached((str) =>
  str.charAt(0).toUpperCase() + str.slice(1)
);

const hyphenateRE = /\B([A-Z])/g;
export const hyphenate = cached((str) =>
  str.replace(hyphenateRE, '-$1').toLowerCase()
);

export const noop = () => {};
export const no = () => false;
export const identity = (_) => _;
export const once = (fn) => {
  let called = false;
  return function (...args) {
    if (!called) {
      called = true;
      return fn.apply(this, args);
    }
  };
};

export const extend = (to, _from) => {
  for (const key in _from) {
    to[key] = _from[key];
  }
  return to;
};

export const toObject = (arr) => {
  const res = {};
  for (let i = 0; i < arr.length; i++) {
    if (arr[i]) {
      extend(res, arr[i]);
    }
  }
  return res;
};