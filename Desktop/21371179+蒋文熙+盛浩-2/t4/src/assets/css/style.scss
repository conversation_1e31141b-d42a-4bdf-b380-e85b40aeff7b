$breakpoints: ('xs': 'only screen and  (max-width: 350px)',
    'sm': 'only screen and  (max-width: 640px)',
    'md': 'only screen and ( max-width: 768px)',
    'lg': 'only screen and ( max-width: 992px)'
) !default;

@mixin respond-to($breakpoint) {
    $query: map-get($breakpoints, $breakpoint);

    @if not $query {
        @error 'No value found for `#{$breakpoint}`. Please make sure it is defined in `$breakpoints` map.';
    }

    @media #{if(type-of($query) == 'string', unquote($query), inspect($query))} {
        @content;
    }
}

:root {
    --body-bg: #e8f6ff;
    --border-radius: 12px;
    --border: 2px solid #33322E;
    --box-shadow: 4px 4px 0px #33322E;
    --box-shadow-reverse: -4px 4px 0px #33322E;
    --padding: 20px 24px;
    --btn-padding: 12px 24px;
    --btn-small-padding: 10px 20px;
    //font
    --font:'DM Sans',"PingFang SC", "Lantinghei SC", "Microsoft YaHei", "HanHei SC", "Helvetica Neue", "Open Sans", Arial, "Hiragino Sans GB", STHeiti, "WenQuanYi Micro Hei", SimSun, sans-serif, HYWenHei-GEW !important;
    --font-color: #33322E;
    --font-size-base: 16px;
    --placeholder: rgba(51, 50, 46, 0.535);
    --font-color-complete: rgba(51, 50, 46, 0.535);
    //bg color
    --bg-normal: #F9F3E5;
    // --bg-normal:#fffbe7;
    --bg-submit: #c2bef0;
    --bg-completed: #D0F4F0;
    --bg-discard: #FFF0EE;
    --bg-deleted: #ddd;
    // --bg-edit:#e8fde6;
    --bg-edit: #fbeef3;

    //dark color
    --normal: #f5d99e;
    --completed: #cae6e2;
    --deleted: #f6b4a8;
    --black: #33322E;

    @include respond-to('md') {
        --font-size-base: 16px;
        --padding: 16px 20px;
        --btn-padding: 12px 18px;
        --btn-small-padding: 10px 15px;
    }
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: all 0.35s;
}

//basic css
html {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    font-family: var(--font);
    /* IE 10+ */
    ::-webkit-scrollbar {
        display: none;

    }
    background: #fff;
    font-size: 16px;
    background: var(--body-bg);
    font-size: var(--font-size-base);
    color: var(--font-color);
    box-sizing: border-box;
    @include respond-to('md') {
        font-family: "PingFang SC", miui, system-ui, -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, sans-serif;
    }
}

::-webkit-input-placeholder {
    color: var(--placeholder);
    font-weight: 400;
}
/* Firefox 4-18 */
:-moz-placeholder {
    color: var(--placeholder);
    font-weight: 400;
}
/* Firefox 19-50 */
::-moz-placeholder {
    color: var(--placeholder);
    font-weight: 400;
}

/* - Internet Explorer 10
   - Internet Explorer Mobile 10-11 */
:-ms-input-placeholder {
    color: var(--placeholder) !important;
    font-weight: 400 !important;
}

/* Edge (also supports ::-webkit-input-placeholder) */
::-ms-input-placeholder {
    color: var(--placeholder);
    font-weight: 400;
}

/* CSS Working Draft */
::placeholder {
    color: var(--placeholder);
    font-weight: 400;
}

button {
    border: 0;
    padding: 0;
    margin: 0;
    background: none;
}

input {
    border-radius: var(--border-radius);
    border: var(--border);
    padding: var(--btn-small-padding);
    display: inline-block;
    cursor: pointer;
    background: #fff;
}

button:focus,
input:focus {
    outline: none;
}

button,
a,
input {
    color: var(--font-color)
}

ul,
li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

h1 {
    font-size: 36px;
    text-align: center;
}

.btn {
    border: var(--border);
    border-radius: var(--border-radius);
    padding: var(--btn-padding);
    text-align: center;
    display: block;
    position: relative;
}

.btn-small {
    padding: var(--btn-small-padding);
    font-size: 14px;
    border: 2px solid var(--black);
}
.container {
    max-width: 700px;
    margin: 0 auto;
    @include respond-to('lg') {
        max-width: 600px;
    }
    @include respond-to('md') {
        max-width: 95%;
    }
}

//main
.todo-app{margin:0 auto;}
.header{margin:0 auto 24px;
    @include respond-to('md') {
        margin:0 auto 16px;
    }
}
.main{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
}
.bg-pattern {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;
    width: 100%;
    height: 100vh;
    background-color: #FFF7DC;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25'%3E%3Cdefs%3E%3Cpattern id='p' width='100' height='100' patternUnits='userSpaceOnUse' patternTransform='scale(0.25)'%3E%3Ccircle data-color='outline' fill='none' stroke='%23828D99' stroke-width='25' cx='50' cy='50' r='.5'%3E%3C/circle%3E%3C/pattern%3E%3C/defs%3E%3Crect fill='url(%23p)' width='100%25' height='100%25'%3E%3C/rect%3E%3C/svg%3E");
    background-size: cover;
    background-position: center center;
    background-repeat: repeat;
    opacity: 0.08;
}

.todo-wrapper {
    width: 100%;
    margin: 40px auto;

    @include respond-to('md') {
        margin: 48px auto;
    }
}

.todo-list-box {
    position: relative;
    background: #fff;
    box-shadow: var(--box-shadow);
    border: var(--border);
    border-radius: var(--border-radius);
    margin: 0;
    padding-top: 0;
    transition: all 0.5s ease;
    max-width: 100%;
    width: 100%;
    overflow: hidden;
}

.todo-list {
    padding: 32px 36px 36px;
    min-height: 381px;
    transition: all .5s ease;
    @include respond-to('md') {
        padding: 20px 16px 24px;
        min-height: 280px;
        min-height: calc(100vh - 272px);
    }
    @include respond-to('xs') {
        min-height: 320px;
    }
}


.title{
    position: relative;
    font-weight: 800;
    -webkit-text-stroke: 2px var(--black);
    color:var(--bg-normal);
    text-shadow: 4px 4px 0 var(--font-color);
    letter-spacing: 1px;
    font-size: 44px;
    margin:0 auto 12px;
    width: 200px;
}
.title{
    img{
     display: inline-block;
     width: auto;
     height: 52px;
     padding:0;
    }

}
.ani-vector{
    position: absolute;
    left:-60px;
    top:4px;
    display: flex;
    justify-content: center;
    align-items: center;
    @include respond-to('md') {
        top: 24px;
    }
    span{
        transform:rotateZ(45deg);
        margin:0 7px;
        width: 10px;
        height: 10px;
        border:2px solid var(--black);
        background-color: var(--bg-submit);
        display: inline-block;
        border-radius: 1px;
        animation: zooming 8s ease-in-out infinite;
        &:nth-child(2){
                animation-delay: 2s;
        }

    }
}

.todo-list li {
    position: relative;
    border: var(--border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 100%;
    transition: all 0.5s ease;
    display: block;
    margin-bottom: 16px;
    // cursor: move;
}

/* Drag css */

// .drag-enter{
//     opacity: 0;
//     transform: translateY(-18px);
// }

// .drag-center-to{
//     opacity: 1;
// }


.drag-enter-active {
    opacity: 0;
    transition: all 0.5s ease;
}

.drag-enter-to {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.5s;
}

.drag-leave {
    opacity: 0;
    transform: translateY(18px);
}

.drag-leave-active {
    position: absolute;
    transition: all 0.35s ease;
}

.drag-move {
    transition: all 0.65s ease;
}

/* End drag css */
.todo-list li:last-child {
    margin-bottom: 0;
}

.todo-list li:hover {
    box-shadow: none;
}

.bar-message {
    position: relative;
    display: block;
    display: flex;
    align-items: center;
    justify-self: flex-start;
    border-bottom: var(--border);
    width: 100%;
    height: 44px;
    line-height: 44px;
    overflow: hidden;
    // &:hover{
    //     background: var(--bg-normal);
    // }
    @include respond-to('md') {
        .btn-allFinish{display: none;}
        .bar-message-text{width: 100%;text-align: center;}
    }
}
.bar-message > div{
    width: 100%;
    height: 100%;
}
.bar-bottom {
    border: 0;
    border-top: var(--border);
}

.add-todo {
    border: var(--border);
}

.add-content-wrapper {
    position: relative;

    @include respond-to('md') {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        max-width: 95%;
        margin: 0 auto;
        z-index: 99;
        padding: 40px 0px 20px;
        background-image: -moz-linear-gradient( 90deg, rgba(234, 246, 252,1) 0%, rgba(234, 246, 252,0) 100%);
        background-image: -webkit-linear-gradient( 90deg, rgba(234, 246, 252,1) 0%, rgba(234, 246, 252,0) 100%);
        background-image: -ms-linear-gradient( 90deg, rgba(234, 246, 252,1) 0%, rgba(234, 246, 252,0) 100%);
    }
}

.tips {
    margin-top: 8px;
    padding-left: 12px;
    transition: all 0.2s;
}
.empty-tips{
    text-align: left;
    max-width: 480px;
    width: 90%;
    margin: 20px auto;
    position: absolute;
    top: 15%;
    left:8%;
    font-size: 16px;
    color: #606060;
    line-height: 1.6;
    letter-spacing: .24px;
    @include respond-to('md') {
        margin-top:0;
        left:6%;
        font-size: 17px;
    }
    @include respond-to('xs') {
        left:6%;
        font-size: 14px;
    }

}
.empty-tips li{
    margin:2px 0 8px;
}
.edit-todo-wrapper {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
}

.edit-todo {
    padding: var(--padding);
    width: 100%;
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    line-height: 1.3;
    overflow-wrap: break-word;
    padding-left: 56px;
    padding-right: 56px;
    -webkit-transition: all 0.25s;
    transition: all 0.25s;
    border: 0;
}

.submit-btn {
    position: relative;
    box-sizing: border-box;
    height: 60px;
    line-height: 60px;
    width: 96px;
    padding: 0;
    text-align: center;
    position: absolute;
    right: 2px;
    top: 2px;
    border: 0;
    border-left: var(--border);
    border-radius: 10px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    cursor: pointer;
    background: #f4d799;
    background: var(--bg-submit);
    transition: all 0.25s;
    font-size: 18px;

    @include respond-to('md') {
    margin:40px 0;
    }
}

.submit-btn:hover {
    box-shadow: none;
}

.add-content {
    width: 100%;
    padding: 0;
    font-size: 18px;
    cursor: text;
    border: var(--border);
    border-radius: var(--border-radius);
    height: 64px;
    line-height: 64px;
    text-indent: 12px;
    padding-right: 118px;
    box-shadow: var(--box-shadow);
    @include respond-to('md') {
        padding-right:108px;
    }
}

.add-content:focus {
    box-shadow: none;
}

.add-content:hover {
    box-shadow: none;
}

.todo-content {
    padding: var(--padding);
    background: var(--bg-normal);
    border-radius: var(--border-radius);
    width: 100%;
    position: relative;
    box-sizing: border-box;
    display: block;
    line-height: 1.5;
    overflow-wrap: break-word;
    padding-left: 56px;
    padding-right: 56px;
    cursor: pointer;
    min-height: 60px;
}

.todo-btn {
    position: absolute;
    background: none;
    display: block;
    cursor: pointer;
    border: var(--border);
    transition: all 0.2s;
    background: #fff;
}

.todo-btn:hover {
    box-shadow: var(--box-shadow);
    transform: translate(-2px, -2px);

}

.btn-edit-submit {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: var(--bg-submit);
    right: 12px;
    top: 16px;
    height: 30px;
    width: 30px;
    border-radius: var(--border-radius);
    font-size: 13px;
}

.btn-delete {
    background: #fff;
    right: 12px;
    top: 16px;
    height: 30px;
    width: 30px;
    border-radius: var(--border-radius);
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-delete:hover {
    background: var(--deleted);
}

.btn-delete img {
    display: block;
    width: 16px;
    height: 16px;
}

.btn-finish {
    left: 14px;
    top: 16px;
    width: 30px;
    height: 30px;
    border-radius: 20px;

    &:hover {
        background: var(--completed);
        box-shadow: var(--box-shadow-reverse);
        transform: translate(2px, -2px);
    }
}

.btn-unfinish img {
    position: relative;
    left: 4px;
    top: 2px;
    width: 26px;
    height: auto;
}

.btn-unfinish {
    left: 14px;
    top: 16px;
    width: 30px;
    height: 30px;
    border-radius: 20px;
    background: var(--completed);

    &:hover {
        box-shadow: var(--box-shadow-reverse);
        transform: translate(2px, -2px);
    }
}

.btn-restore {
    background: #fff;
    right: 12px;
    top: 16px;
    height: 30px;
    width: 30px;
    border-radius: var(--border-radius);
    border: var(--border);
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-restore img {
    display: block;
    width: 16px;
    height: auto
}

.btn-allFinish {
    display: inline-block;
    background: var(--completed);
    border: 0;
    border-radius: 0;
    border-top-left-radius: var(--border-radius);
    border-right: var(--border);
    margin: 0;
    min-width: 24%;
}

.bar-message-text {
    display: inline-block;
    text-align: center;
    font-weight: 600;
    padding: 0;
    padding-left: 12px;
    padding-right: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 44px;
    width: 100%;
    text-align: left;
    cursor:pointer;
}
.slogan-input{
    width: 100%;
    border:none;
    padding: 0 12px;
    background: var(--bg-normal);
    border-radius:0;
    display: inline-block;
    position:relative;
    line-height: 44px;
    height: 44px;
    cursor: text;
}
.slogan-btn{
    position: absolute;
    right: 16px;
    top: 6px;
}

/**
 * Footer
 */
 .nav {
    position: fixed;
    right: 36px;
    top: 8px;
    z-index: 9999;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    @media (max-width: 767px) {
        right: 16px;
        top: 4px;
    }
 }
 .about{
     .info-ico{
        position: relative;
        z-index: 9999;
        margin:10px;
        display: flex;
        align-items: center;
        justify-items: flex-start;
        cursor: pointer;
        transition: all 0.3s;
        img{
            width:auto;height:40px;display: inline-block;
            border-radius: 48px;
        }
        &:hover{
           font-weight: bold!important;
        }
     }
 }
 .github{
    font-size: 0;
    display: inline-block;
    padding: 6px;
 }
.popup{
    display: block;
    position: absolute;
    top: 36px;
    right: 24px;
    z-index: 999;
    transition: 0.25s;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    box-shadow: var(--box-shadow);
    border:2px solid var(--black);
    padding:24px;
    background-color: #fff;
    width: 220px;
    .author{
        font-size: 16px;
        font-weight: bold;
        font-style: normal;
        margin:0px 0 12px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: start;
        &-desc{
            font-size: 15px;
            font-style: normal;
            line-height: 1.4;
            color: #606060;
        }
        img{
            margin-right: 8px;
            width: 44px;
            height: 44px;
            border-radius: 48px;
            display: inline-block;
        }

    }
    p{
        font-size: 12px;
        font-style: normal;
        line-height: 1.5;
        padding:2px;
        text-align: center;
        margin: -8px auto 8px;
        span{
            opacity: 0.75;
        }
    }

    img{
        width: 148px;
        height: 148px;
        border-radius: 8px;
        -webkit-border-radius: 8px;
        -moz-border-radius: 8px;

    }
    @include respond-to('md') {
        img{
            width: 160px;
            height: 160px;
        }
    }
}
.donate{
    margin:12px 0;
}
.zanshan{
    margin:12px 0 0;
}
.zanshan p{
    font-size:14px;
    text-align:left;
    display:block;
    margin:12px 0 6px;
}
img.zanshan-qr{
    width:100%;
    height:auto;
}
.donate a{
    display: block;
    position: relative;
    outline: none;
    width: 100%;
}
.language{
    position: relative;
    display: inline-block;
    margin-left:4px;
    color:#606060;
    text-decoration: none;
    font-size: 12px;
    display:inline-block;
    z-index: 999;
}
.language a{
    position: relative;
    font-size: 14px;
    color:#606060;
    text-decoration: none;
    display:inline-block;
    padding:4px;
    outline: none;
}
.language a.active{
    font-size: 16px;
    color: #33322E;
    font-weight: 600;
}

.social {
    margin: 0 auto;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: flex-start;
    margin:12px 0;
}
.social-link{
    opacity: 0.65;
    outline: none;
}.social-link:hover{
    opacity: 1;
}
.social-link img {
    fill: #33322E;
    width: auto;
    height: 20px;
    color: #33322E;
}
.ic-social {
    fill:#33322E;
    width: auto;
    height: 20px;
}

.inspiration{
    position: relative;
    text-align: left;
    font-style: normal;
    display: block;
    margin:6px auto 0px;
    width: 100%;
    font-size: 14px;
    letter-spacing: 0.36px;
    color: #606060;
}
a.inspiration:focus{
    -webkit-user-modify:unset;
}
.inspiration img{
    display: inline-block;
    margin-right: 5px;
    width: 20px;
    height: 20px;
    border-radius: 2px;
}



// side bar
.side-bar{
    position: absolute;
    left:calc( 100% + 28px);
    top:0;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    border:var(--border);
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: all 0.3s;
    overflow: hidden;
    z-index: 999;
    @include respond-to('md') {
        position: fixed!important;
        top:unset;
        bottom:108px;
        left: unset;
        right: -2px;
        box-shadow: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}


.action-download:hover, .action-download.selected {
    background: #f8d966;
}
.action-import:hover, .action-import.selected {
    background: #f8d966;
}
@media only screen and (max-width: 768px){
  .todo-list {
      min-height: 480px;
  }
}




.side-bar:hover{
    box-shadow: unset!important;
}
.side-shortcut{
    display: block;
    width: 100%;

}
.shortcut-switch{
    overflow: hidden;
    cursor: pointer;
    width: 100%;
    @include respond-to('md') {
        font-size: 14px;
        .shortcut-name{display: none!important;}
    }
}
.shortcut-switch span{
    padding:10px 14px;
    display: block;
}
.shortcut-title{
    background: var(--completed);
    text-align: center;
    box-sizing: border-box;
    border-bottom:var(--border);
    width: 100%;
    font-weight: bold;
}
span.shortcut-name{
   display: none;
}
.todo-footer-box {
    position: relative;
    height: 100%;

    @include respond-to('md') {
        // position: fixed!important;
        // bottom:0;
        // left: 0;
        // right: 0;
    }
}
.fold{
    @include respond-to('md') {
        border-radius: 0px;
        box-shadow:none;
        overflow: hidden;
        padding:0;
        .shortcut-switch span{
            border:0;
            padding:12px 16px;
        }
    }
    &~.todo-footer-box{display: none;}
    .shortcut-title{
        background: var(--deleted);

    }
    span.shortcut-name{
        display: block;
        writing-mode: vertical-lr;
    }
}

.todo-func-list {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    font-size: 14px;
    padding: 0;
    @include respond-to('md') {
    }
}

.todo-func-list li {
    cursor: pointer;
    margin: 0 auto;
    transition: all .25s;
    width: 100%;
    @include respond-to('md') {
        margin: 0;
        display: inline-block;
    }
}

.todo-func-list li input {
    transition: all 0.25s;
    width: 100%;
    border-radius: 0;
    border:0;
    box-shadow: unset;
    border-top:1px solid var(--black);
    transform: unset!important;
}
.todo-func-list.filter li:first-child input{
    border:0;
}
.todo-func-list li:first-child {
    min-width: 60px;
    font-weight: 400;
    border:0;
    @include respond-to('md') {
        display: block;
    }
}

.todo-func-list li:last-child {
    border: 0;
    margin-right: 0;
}

.filter {
    width: 100%;
    // border-bottom:var(--border);
}


//classification
.completed {
    background: var(--bg-completed);
    color: var(--font-color-complete);
    text-decoration: line-through;
}

.deleted {
    background: var(--bg-deleted);
}

.empty {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    // border: 3px solid #F6A89E;
    -webkit-animation-name: headShake;
    animation-name: headShake;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.selected {
    box-shadow: var(--box-shadow);
    transform: translate(-2px, -2px);
}

.action-showAll:hover,
.action-showAll.selected {
    background: var(--bg-submit);
}

.action-progress:hover,
.action-progress.selected {
    background: var(--normal);
}

.action-completed:hover,
.action-completed.selected,
.completed-all:hover {
    background: var(--completed);
}

.action-progress:hover,
.action-progress.selected {
    background: var(--normal);
}

.action-deleted:hover,
.action-deleted.selected,
.completed-clear:hover,
.clear-all:hover {
    background: var(--deleted);
}

#panda{
    position:absolute;
    width:200px;
    height:200px;
    right:-72px;
    bottom:-2px;
}
//pendulums

.pendulums{
    position:absolute;
    width:50px;
    height:72px;
    right:-56px;
    bottom:-2px;
    perspective:640px;
  }

  .pendulum{
    position:absolute;
    width:50px;
    height:70px;
    right:44%;
    bottom:0;
    left:50%;
    transform: translateZ(-300px) translateX(-50%);
    &:after{
        content:"";
        width: 8px;
        height: 8px;
        background-color: var(--bg-submit);
        position: absolute;
        top:2px;
        left: 0;
        right: 0;
        margin: 0 auto;
        transform: rotate(45deg);
        border: 3px solid var(--black);
    }
  }

  .pendulum.shadow{
      display: none;
    top:263%;
    left:-65%;
    bottom:0;
    transform-origin: 50% 0;
  transform:translateX(-50%) scaleY(-1) scaleZ(2) rotateY(-30deg) rotateX(-75deg) translateZ(300px);
  }

  .pendulum .bar{
    position:absolute;
    left:50%;
    transform:translateX(-50%);
    top:-5px;
    display:block;
    width: 26px;
    height:30px;
    background:#d0f4f0;
    border:3px solid #33322E;
    border-radius: 15px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
  }

  .pendulum .string{
    position:absolute;
    left:50%;
    transform:translateX(-50%);
    width:3px;
    height:72px;
    background:var(--black);
  }

  .pendulum .weight{
    position:absolute;
    left:50%;
    transform:translateX(-50%);
    bottom:14px;
    width:26px;
    height:26px;
    border-radius:50%;
    background:#f8d966;
    border:3px solid #33322E;
  }

  .pendulum .motion{
    position:absolute;
    height:90px;
    width:50px;
    transform-origin:50% 10px;
    animation: swing 1800ms infinite ease-in-out;
  }

  .pendulum.shadow .bar, .pendulum.shadow .string, .pendulum.shadow .weight{
    background:#3d3a34;
  }
  @keyframes swing{
    0%{
    transform:rotate(-45deg);
    }
    50%{

    transform:rotate(45deg);
    }
    100%{

    transform:rotate(-45deg);
    }
  }
//End Pendulums
//animation
.animated {
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.tips-enter-active {
    transition: all .75s ease;
}

.tips-leave-active {
    transition: all .35s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.tips-enter,
.tips-leave-to {
    transform: translateX(8px);
    opacity: 0;
}




@-webkit-keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg)
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg)
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg)
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg)
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg)
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg)
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg)
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg)
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.zooming{
    animation: zooming 1.5s ease-in-out infinite;
}
@keyframes zooming {
    0% {
        transform: scale(1) rotateZ(45deg);
    }

    20% {
        transform: scale(1) rotateZ(45deg);
    }

    30% {
        transform: scale(1) rotateZ(135deg);
    }

    70% {
        transform: scale(1) rotateZ(135deg);
    }

    80% {
        transform: scale(1) rotateZ(45deg);
    }

    100% {
        transform: scale(1) rotateZ(45deg);
    }
}

.popIn {
    -webkit-animation-name: popIn;
    animation-name: popIn
}

@keyframes popIn {
    0% {
        -webkit-transform: scale3d(0, 0, 0);
        transform: scale3d(0, 0, 0);
        opacity: 0
    }

    20% {
        opacity: 1
    }

    40% {
        -webkit-animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
        animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
        -webkit-transform: scale3d(1.08, 1.08, 1.08);
        transform: scale3d(1.08, 1.08, 1.08)
    }

    60% {
        -webkit-animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    80% {
        -webkit-animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03)
    }

    100% {
        -webkit-animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}


.popOut {
    -webkit-animation-name: popOut;
    animation-name: popOut;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s
}

@keyframes popOut {
    0% {
        -webkit-animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    60% {
        -webkit-animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        -webkit-transform: scale3d(1.08, 1.08, 1.08);
        transform: scale3d(1.08, 1.08, 1.08)
    }

    80% {
        opacity: 1;
        -webkit-animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
        animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1)
    }

    100% {
        opacity: 0;
        -webkit-animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
        animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
        -webkit-transform: scale3d(0.3, 0.3, 0.3);
        transform: scale3d(0.3, 0.3, 0.3)
    }
}