<template>

    <div class="custom-alert">
        <div class="custom-alert-title">输入高级搜索信息</div>
        <!-- <div class="custom-alert-content">选择高级搜索信息</div> -->
        <div class="inLine">
            <div style="flex:1;">输入关键词</div>
            <CardInput @update="keyUpdate" class="inputBox" />
        </div>
        <div class="inLine">
            <div style="flex:1;">输入作者</div>
            <CardInput @update="authorUpdate" class="inputBox" />
        </div>

        <!-- <Datepicker v-model="startdate" @update:modelValue="onDateChange" input-class="custom-input"
            wrapper-class="custom-wrapper" class="my-date" /> -->
        <div class="inLine">
            起始日期：
            <VueDatePicker @update:modelValue="onDateChange" v-model="startdate" style="flex: 2;margin-right: 10px;"></VueDatePicker>
            结束日期：
            <VueDatePicker @update:modelValue="onDateChange" v-model="enddate" style="flex: 2;"></VueDatePicker>

        </div>

        <div class="inLine">
            <label style="flex:1;">
                <input type="checkbox" v-model="isEnabled" />
                启用A刊过滤
            </label>
        </div>

        <div class="custom-alert-buttons">
            <button class="custom-alert-btn cancel" @click="cancel">取消</button>
            <button class="custom-alert-btn confirm" @click="func1">确定</button>
        </div>
    </div>

</template>


<script>
import CardInput from './CardInput.vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import request from '@/utils/api';

export default {
    components: {
        CardInput, VueDatePicker
    },
    data() {
        return {
            data: '',
            currentTags: [],
            currentAuthors: <AUTHORS>
            isEnabled: true,
            startdate: null,
            enddate: null
        }
    },
    methods: {
        keyUpdate(newTags) {
            this.currentTags = newTags
        },
        authorUpdate(newTags) {
            this.currentAuthors = newTags
        },
        func1() {
            console.log('当前标签：', this.currentTags)
            this.$emit('saveAdvance', {
                "tags": this.currentTags,
                "authors": this.currentAuthors,
                "startDate": this.startdate,
                "endDate": this.enddate,
                "isEnabled": this.isEnabled
            });
            // request.post('searchAdvance', JSON.stringify({ key1: "value1", key2: ['111', '222'] }))
        },
        onDateChange(newDate) {
            console.log('选择的日期是:', newDate)
        },
        cancel() {
            this.$emit('cancel');
        }

    }
}

</script>


<style scoped>
.custom-alert {
    max-width: 600px;
}


.inLine {
    display: flex;
    flex-direction: row;
    text-align: center;
    align-items: center;
    margin-top: 10px;
}

.inputBox {
    flex: 5;
    margin-left: 10px;
}

.custom-input {
    border: 1px solid #ccc;
    padding: 8px;
    border-radius: 6px;
}

.custom-wrapper {
    display: inline-block;
}

.my-date {
    background-color: aqua;
}
</style>