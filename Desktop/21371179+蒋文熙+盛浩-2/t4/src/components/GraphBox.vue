<template>
    <div class="todo-list-box graph-box">
        <GraphComponent v-if="graph" :graphData="graph" />
    </div>
</template>

<script>
import GraphComponent from './GraphComponent.vue';

export default {
    name: '<PERSON>raph<PERSON><PERSON>',
    components: {
        GraphComponent
    },
    props: [
        'graph'
    ],
    data() {
        return {
        }
    },
    methods: {

    },
    mounted() {
        console.log(JSON.stringify(this.graph, null, 2));
        
    }
}

</script>

<style scoped>
.graph-box {
    min-height: 700px;
    margin: 10px;
}
</style>