<template>
    <div class="tag-input-wrapper" @click="focusInput">
        <div class="tag" v-for="(tag, index) in tags" :key="index">
            {{ tag }}
            <span class="remove" @click.stop="removeTag(index)">×</span>
        </div>
        <input ref="inputRef" v-model="input" @keydown.enter.prevent="addTag" @keydown.delete="handleBackspace"
            placeholder="输入并回车" />
    </div>
</template>

<script>
export default {
    data() {
        return {
            tags: [],
            input: '',
        }
    },
    methods: {
        addTag() {
            const value = this.input.trim()
            if (value) {
                this.tags.push(value)
                this.$emit('update', this.tags)
                this.input = ''
            }
        },
        removeTag(index) {
            this.tags.splice(index, 1)
            this.$emit('update', this.tags)
        },
        handleBackspace() {
            if (this.input === '' && this.tags.length > 0) {
                this.tags.pop()
                this.$emit('update', this.tags)
            }
        },
        focusInput() {
            this.$refs.inputRef && this.$refs.inputRef.focus()
        }
    }
}
</script>

<style scoped>
.tag-input-wrapper {
    display: flex;
    /* flex-wrap: wrap; */
    align-items: center;
    border: 1.5px solid #33322E;
    padding: 6px;
    border-radius: var(--border-radius);
    cursor: text;
    min-height: 52.5px;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
}

.tag-input-wrapper::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
}

.tag {
    display: inline-flex;
    align-items: center;
    background: #d0eaff;
    border-radius: 3px;
    padding: 4px 8px;
    margin: 4px 4px 4px 0;
    flex-shrink: 0;
}

.remove {
    margin-left: 6px;
    cursor: pointer;
}

input {
    flex: 1;
    min-width: 100px;
    border: none;
    outline: none;
    padding: 4px;
    margin: 4px 0;
    /* flex: 0 0 auto; */
}
</style>