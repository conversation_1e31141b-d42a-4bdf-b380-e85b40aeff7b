<template>
    <div v-if="mode === 'read'">
        <div class="about">
            <h1>辅助阅读</h1>
        </div>
        <div class="container header">
            <div class="add-content-wrapper container">
                <input type="text" placeholder="输入论文名称" class="add-content" v-model="paperName"> <!---->
                <input type="file" ref="pdfInput" accept="application/pdf" style="display: none"
                    @change="onFileSelected" />
                <!-- 自定义按钮样式 -->
                <button @click="triggerFileInput" class="upload-btn btn btn-secondary input_btn">
                    <i class="bi bi-arrow-bar-up"></i>
                </button>

                <button type="button" class="btn submit-btn" @click="searchName">搜索
                </button>
            </div>
        </div>

        <div class="container main">
            <div class="todo-list-box">
                <div class="bar-message" @click="clearAll">
                    <div class="bar-message-text bar-clear">清空</div>
                    <div class="bar-message-text" style="flex: 4;">{{ status===1 ?  "选择论文" : "已选择论文" }}</div>
                </div>
                <ul v-show="status!==1" mode="in-out" class="todo-list">
                    <li v-for="(item, index) in paperList" :key="index">
                        <PaperCard :Name="item['title']" @delete="onDelCard(index)"/>
                    </li>
                </ul>
                <ul v-show="status===1" mode="in-out" class="todo-list" @scroll="handelScroll" ref="scrollList">
                    <li v-for="(item, index) in previewList" :key="index">
                        <PaperPreview :Title="item['title']" :Date="item['date']" :Authors="item['authors']" :-url="item['url']" :chose="item['chose']" @changeChoose="(chosed) => onChangeChoose(index, chosed)"/>
                    </li>
                </ul>
                <div class="bar-message bar-bottom">
                    <div v-if="status===0" class="bar-message-text gen-rough-btn" @click="genRough">论文总结</div>
                    <div v-if="status===0" class="bar-message-text gen-detail-btn" @click="genDetail">要点分析</div>
                    <div v-if="status===1" class="bar-message-text" style="text-align: center;" @click="confirmChoose">确认选择</div>

                </div>
            </div>
            <div class="footer side-bar" @click="toggleMode">
                <div class="side-shortcut">
                    <div class="shortcut-switch">
                        <span class="shortcut-title"><i class="bi bi-arrow-repeat" style="font-size: 15px;"></i></span> 
                    </div>
                </div>
            </div>
        </div>
    </div>







    <div v-if="mode === 'write'">
        <div class="about">
            <h1>综述生成</h1>
        </div>
        <div class="container header">
            <div class="add-content-wrapper container">
                <input type="text" placeholder="输入论文名称" class="add-content" v-model="paperName"> <!---->
                <input type="file" ref="pdfInput" accept="application/pdf" style="display: none"
                    @change="onFileSelected" />
                <button @click="triggerFileInput" class="upload-btn btn btn-secondary input_btn">
                    <i class="bi bi-arrow-bar-up"></i>
                </button>
                <button @click="advancedSearch" class="upload-btn btn btn-secondary advance_btn">
                    <i class="bi bi-ui-checks-grid"></i>
                </button>

                <button type="button" class="btn submit-btn" @click="searchName">搜索
                </button>
            </div>
        </div>

        <div class="container main">
            <div class="todo-list-box">
                <div class="bar-message" @click="clearAll">
                    <div class="bar-message-text bar-clear">清空</div>
                    <div class="bar-message-text" style="flex: 4;">{{ status===1 ?  "选择论文" : "已选择论文"}}</div>
                </div>
                <ul v-show="status!==1" mode="in-out" class="todo-list">
                    <li v-for="(item, index) in paperList" :key="index">
                        <PaperCard :Name="item['title']" @delete="onDelCard(index)"/>
                    </li>
                </ul>
                <ul v-show="status===1" mode="in-out" class="todo-list" @scroll="handelScroll" ref="scrollList">
                    <li v-for="(item, index) in previewList" :key="index">
                        <PaperPreview :Title="item['title']" :Date="item['date']" :Authors="item['authors']" :-url="item['url']":chose="item['chose']" @changeChoose="(chosed) => onChangeChoose(index, chosed)"/>
                    </li>
                </ul>

                <div class="bar-message bar-bottom">
                    <input v-if="status===0" type="text" placeholder="综述生成领域" class="bar-message-text input_field" v-model="field">
                    <div v-if="status===0" class="bar-message-text" style="text-align: center; flex: 1;"  @click="genSurvey">
                        生成综述
                    </div>
                    <div v-if="status===1" class="bar-message-text" style="text-align: center;"  @click="confirmChoose">
                        确认选择
                    </div>
                </div>

            </div>
            <div class="footer side-bar" @click="toggleMode">
                <div class="side-shortcut">
                    <div class="shortcut-switch">
                        <span class="shortcut-title">
                            <i class="bi bi-arrow-repeat" style="font-size: 15px;"></i>
                        </span> 
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showAlert" class="overlay" @click="showAlert=false">
            <AdvancedSearch @click.stop @cancel="showAlert=false" @saveAdvance="searchAdvance"/>
        </div>
    </div>
    
</template>


<script>

import request from '@/utils/api';
import PaperCard from './PaperCard.vue';
import PaperPreview from './PaperPreview.vue';
import AdvancedSearch from './AdvancedSearch.vue';
import router from '@/router';


export default {
    name: 'MainBody',
    components: {
        PaperCard,
        PaperPreview,
        AdvancedSearch
    },
    data() {
        return {
            paperName: "",
            paperList: [],
            mode: "write", 
            status: 0, // 1:选择论文 0: 已经选择的
            previewList: [],
            page: 0,
            cacheName: "",
            field: "",
            showAlert: false,
            advancedData: []
        }
    },
    methods: {
        searchName() {
            this.page = 0;
            console.log(this.advancedData.length);
            
            if (this.advancedData.length === 0) {
                request.get(`searchTitle?name=${this.paperName}&page=${this.page}`).then(result => {
                    this.previewList = this.previewList.concat(result);
                })
            }
            else {
                request.post(`searchAdvance?name=${this.paperName}&page=${this.page}`, this.advancedData).then(result => {
                    this.previewList = this.previewList.concat(result);
                })
            }
            this.status = 1;
            this.cacheName = this.paperName;
            this.paperName = "";
            this.page = 1;
        },
        onFileSelected(event) {
            const file = event.target.files[0];
            if (!file || file.type !== 'application/pdf') {
                alert('请上传 PDF 文件');
                return;
            };
            
            if (this.mode==='read') {
                this.paperList = [];
            }
            var name = file.name
            if (name.endsWith('.pdf')) name = name.slice(0, -4);
            this.paperList.push({
                'title': name,
                "type": "upload",
                "url": file
            })
            console.log(Object.prototype.toString.call(file))
        },
        triggerFileInput() {
            this.$refs.pdfInput.click(); // 打开文件选择框
        },
        clearAll() {
            if (this.status===0) {
                this.paperList = [];
                this.field = "";
            }
            else 
                this.previewList = [];
        },
        advancedSearch() {
            this.showAlert = true;
        },
        toggleMode() {
            if (this.mode === 'read') {
                this.mode = 'write';
            } else {
                this.mode = 'read';
            }
            this.advancedData = [];
            this.clearAll();
        },
        confirmChoose() {
            this.paperList.push(...this.previewList.filter(item => item.chose));
            this.previewList = [];
            this.status = 0;
        },
        confirmPaper() { 
            const payload = {
                item: this.paperList.map(item => ({
                    type: item.type,
                    title: item.title,
                    url: item.type==='upload'? null : item.url,
                    authors: item.type==='upload'? null : item.authors,
                    date: item.type==='upload'? null : item.date,
                }))
            }

            const myForm = new FormData();
            myForm.append('json', JSON.stringify(payload));

            this.paperList.forEach((item, index) => {
                if (item.type === 'upload') {
                    myForm.append(`file_${index}`, item.url);
                }
            })

            return myForm;
        },
        async genSurvey() {
            if (this.field === "") {
                alert('请输入综述领域')
                return;
            }
            const formdata = this.confirmPaper();
            const res = await request.post(`uploadFile?type=survey&field=${this.field}`, formdata);
            this.field = "" 
            await this.$router.push(`generate?id=${res['id']}`);
        },
        async genDetail() {
            const formdata = this.confirmPaper();
            const res = await request.post('uploadFile?type=detail', formdata);
            await this.$router.push(`generate?id=${res['id']}`);
        },
        async genRough() {
            const formdata = this.confirmPaper();
            const res = await request.post('uploadFile?type=rough', formdata);           
            await this.$router.push(`generate?id=${res['id']}`);
        },
        onChangeChoose(index, status) {
            this.previewList[index]['chose'] = status
            if (this.mode === 'read' && status == true) {
                this.previewList.forEach((v, i) => {
                    if (i != index) {
                        v['chose'] = false;
                    }
                    
                })                
            }
            // console.log(this.previewList);
        },
        onDelCard(index) {
            this.paperList.splice(index, 1);
        },
        async handelScroll() {
            const el = this.$refs.scrollList;
            if (!el) return;
            if (el.scrollTop+ el.clientHeight >= el.scrollHeight - 5) {
                if (this.advancedData.length === 0) {
                    const res = await request.get(`searchTitle?name=${this.cacheName}&page=${this.page}`);
                    this.previewList = this.previewList.concat(res);
                }
                else  {
                    const res = request.post(`searchAdvance?name=${this.paperName}&page=${this.page}`, this.advancedData);
                    this.previewList = this.previewList.concat(res);
                }
                
                this.page += 1;
            } 
        },
        searchAdvance(data) {
            this.advancedData = data;
            this.showAlert = false
            // request.post('searchAdvance', data)
        }
    },
}

</script>

<style scoped>
.todo-list {
    max-height: 400px;
    overflow-y: auto;
}

.bar-clear {
    background-color: var(--deleted);
    border-right: var(--border);
    flex: 1;
}

.bar-bottom {
    background-color: var(--completed);
}

.input_btn {
    /* color: #ff0000; */
    position: absolute;
    right: 100px;
    align-self: anchor-center;
    align-items: center;
    /* top: 50%;
        transform: translateY(-50%);
        transform: translateX(-400%); */
    z-index: 9999;
    height: 30px;
    width: 30px;
    padding: 0%;
    margin-right: 10px;
    color: darkgray;
    border-color: darkgray;
}

.advance_btn {
    position: absolute;
    right: 100px;
    align-self: anchor-center;
    align-items: center;
    /* top: 50%;
        transform: translateY(-50%);
        transform: translateX(-400%); */
    z-index: 99;
    height: 30px;
    width: 30px;
    padding: 0%;
    margin-right: 50px;
    color: darkgray;
    border-color: darkgray;
}

.shortcut-title {
    border-bottom: inherit;
    background-color: var(--bg-submit);
}

.side-bar {
    border-radius: 50%;
}

.bar-message{
    display: flex;
}

/* .bar-message-text:hover{
    background-color: var(--completed);
} */

.submit-btn{
    background-color: var(--completed);
}


.gen-rough-btn{
    flex: 1;
    text-align: center;
}

.gen-detail-btn{
    flex: 1;
    border-left: var(--border);;
    text-align: center;
}

.input_field{
    flex: 3;
    border-left: none;
    border-radius: 0;
    border-top: none;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 确保遮罩在最上层 */
}

</style>
