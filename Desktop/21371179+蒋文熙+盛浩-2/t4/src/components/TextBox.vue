<template>
    <div class="todo-list-box">
        <button class="copy-button" @click="copyToClipboard">复制</button>

        <div class="text-box">
            <div class="mdcontent" ref="mdcontent" v-html="renderedMarkdown"></div>
        </div>
    </div>
</template>

<script>
import { marked } from 'marked';
// 导入 KaTeX 的自动渲染扩展
import renderMathInElement from 'katex/dist/contrib/auto-render';
import 'katex/dist/katex.min.css';


export default {
    name: 'TextBox',
    props: { // 推荐使用对象形式定义 props，并添加类型校验
        data: {
            type: String,
            default: ''
        }
    },
    // data() { 
    //     return {}
    // },
    methods: {
        copyToClipboard() {
            // 考虑兼容性，可以添加错误处理
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(this.data)
                    .then(() => {
                        // 可以考虑使用更友好的提示方式，如 Message 组件
                        alert('已复制到剪切板');
                    })
                    .catch(err => {
                        console.error('复制失败:', err);
                        alert('复制失败');
                    });
            } else {
                // 降级处理（如果需要支持旧浏览器）
                // 使用 execCommand 的降级方案
                const textarea = document.createElement('textarea');
                textarea.value = this.data;
                textarea.style.position = 'fixed'; // 防止页面滚动影响
                textarea.style.opacity = 0; // 隐藏 textarea
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();

                try {
                    const success = document.execCommand('copy');
                    if (success) {
                        alert('已复制到剪切板');
                    } else {
                        alert('复制失败');
                    }
                } catch (err) {
                    console.error('复制异常:', err);
                    alert('复制失败');
                }

                document.body.removeChild(textarea);
            }
        },
        // 封装 KaTeX 渲染逻辑
        renderKatex() {
            // 使用 $nextTick 确保 v-html 渲染完成后再执行 KaTeX
            this.$nextTick(() => {
                const element = this.$refs.mdcontent;
                if (element) {
                    try {
                        renderMathInElement(element, {
                            delimiters: [
                                { left: '$$', right: '$$', display: true },
                                { left: '$', right: '$', display: false },
                                { left: '\\(', right: '\\)', display: false },
                                { left: '\\[', right: '\\]', display: true }
                            ],
                            throwOnError: false
                        });
                    } catch (error) {
                        console.error("KaTeX rendering failed:", error);
                    }
                } else {
                    console.warn("mdcontent ref not found during KaTeX render.");
                }
            });
        }
    },
    computed: {
        renderedMarkdown() {
            // 确保 data 存在，避免 marked(null) 或 marked(undefined) 出错
            return marked(this.data || '');
        }
    },
    watch: {
        // 监听 data prop 的变化
        data(newData, oldData) {
            if (newData !== oldData) {
                // data 变化后，renderedMarkdown 会自动重新计算并更新 v-html
                // 我们只需要在 DOM 更新后重新运行 KaTeX
                this.renderKatex();
            }
        }
    },
    mounted() {
        // 组件挂载后，首次渲染 KaTeX
        this.renderKatex();
    },
}
</script>

<style scoped>
.text-box {
    position: relative;
    /* 为了使子元素 absolute 定位基于此容器 */
    height: 650px;
    /* width: 700px; */
    margin: 10px;
    overflow-y: auto;
    margin-top: 40px;
    /* 默认文本左对齐 (通常是浏览器默认，但明确设置更好) */
    text-align: left;
}

.mdcontent {
    padding-left: 10px;
    padding-right: 10px;
}

/* 使用 :deep() 选择器来定位 v-html 内部的标题元素 */
.mdcontent :deep(h1),
.mdcontent :deep(h2),
.mdcontent :deep(h3),
.mdcontent :deep(h4),
.mdcontent :deep(h5),
.mdcontent :deep(h6) {
    text-align: center;
    /* 标题居中 */
}

/* (可选) 确保段落是左对齐的，如果父级或全局样式覆盖了 */
.mdcontent :deep(p) {
    text-align: left;
    /* 正文段落左对齐 */
}

/* (可选) 确保列表也是左对齐 */
.mdcontent :deep(ul),
.mdcontent :deep(ol) {
    text-align: left;
}


.copy-button {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    padding: 5px 10px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.copy-button:hover {
    background-color: #66b1ff;
}
</style>
