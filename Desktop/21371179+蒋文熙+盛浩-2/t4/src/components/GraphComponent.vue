<template>
    <div>
        <div ref="networkContainer" class="graph-container"></div>
        <div v-if="selectedInfo" class="info-panel">
            <h3>{{ selectedInfo.type === 'node' ? '节点信息' : '关系信息' }}</h3>
            <p>{{ selectedInfo.text }}</p>
        </div>
    </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { Network } from 'vis-network/standalone'
import 'vis-network/styles/vis-network.css'

export default {
    name: 'KnowledgeGraph',
    props: {
        graphData: {
            type: Object,
            required: true
        }
    },
    setup(props) {
        const networkContainer = ref(null)
        const selectedInfo = ref(null)
        let networkInstance = null

        const colorMap = {
            EVENT: '#97C2FC',
            ORGANIZATION: '#FFD700',
            PERSON: '#FF6F61',
            default: '#CCCCCC'
        }

        const getColorByType = (type) => colorMap[type] || colorMap.default

        const highlightNode = (nodeId) => {
            const updatedNodes = props.graphData.nodes.map((n) => ({
                id: n.id,
                color: {
                    background: n.id === nodeId ? getColorByType(n.type) : '#ddd',
                    border: n.id === nodeId ? '#2B7CE9' : '#aaa'
                }
            }))
            networkInstance.body.data.nodes.update(updatedNodes)

            const updatedEdges = props.graphData.edges.map((e) => ({
                id: e.id,
                color: { color: '#ccc' }
            }))
            networkInstance.body.data.edges.update(updatedEdges)
        }

        const highlightEdge = (edgeId) => {
            const targetEdge = props.graphData.edges.find(e => e.id === edgeId)
            const connectedNodeIds = targetEdge ? [targetEdge.from, targetEdge.to] : []

            const updatedEdges = props.graphData.edges.map((e) => ({
                id: e.id,
                color: { color: e.id === edgeId ? '#2B7CE9' : '#ccc' }
            }))
            networkInstance.body.data.edges.update(updatedEdges)

            const updatedNodes = props.graphData.nodes.map((n) => ({
                id: n.id,
                color: {
                    background: connectedNodeIds.includes(n.id) ? getColorByType(n.type) : '#ddd',
                    border: connectedNodeIds.includes(n.id) ? '#2B7CE9' : '#aaa'
                }
            }))
            networkInstance.body.data.nodes.update(updatedNodes)
        }

        const resetHighlight = () => {
            const resetNodes = props.graphData.nodes.map((n) => ({
                id: n.id,
                color: {
                    background: getColorByType(n.type),
                    border: '#2B7CE9'
                }
            }))
            const resetEdges = props.graphData.edges.map((e) => ({
                id: e.id,
                color: { color: '#2B7CE9' }
            }))
            networkInstance.body.data.nodes.update(resetNodes)
            networkInstance.body.data.edges.update(resetEdges)
        }

        onMounted(() => {
            const formattedEdges = props.graphData.edges.map((e) => ({
                ...e,
                length: e.weight ? 200 / e.weight : 200,
                color: { color: '#2B7CE9' }
            }))

            const formattedNodes = props.graphData.nodes.map((n) => ({
                ...n,
                color: {
                    background: getColorByType(n.type),
                    border: '#2B7CE9'
                }
            }))

            networkInstance = new Network(networkContainer.value, {
                nodes: formattedNodes,
                edges: formattedEdges
            }, {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: { size: 14, color: '#333' }
                },
                edges: {
                    arrows: {
                        to: { enabled: true, scaleFactor: 0.5 }
                    },
                    smooth: true
                },
                interaction: {
                    dragNodes: true,
                    dragView: true,
                    zoomView: true
                },
                physics: {
                    enabled: true,
                    solver: 'forceAtlas2Based',
                    stabilization: {
                        iterations: 100,
                        fit: true
                    }
                },
                manipulation: {
                    enabled: false
                },
                layout: {
                    improvedLayout: true
                }
            })

            networkInstance.on('click', function (params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0]
                    const node = props.graphData.nodes.find(n => n.id === nodeId)
                    selectedInfo.value = {
                        type: 'node',
                        text: node.detail || '这个节点没有额外说明'
                    }
                    highlightNode(nodeId)
                } else if (params.edges.length > 0) {
                    const edgeId = params.edges[0]
                    const edge = props.graphData.edges.find(e => e.id === edgeId)
                    selectedInfo.value = {
                        type: 'edge',
                        text: edge.detail || '这个关系没有额外说明'
                    }
                    highlightEdge(edgeId)
                } else {
                    selectedInfo.value = null
                    resetHighlight()
                }
            })
        })

        watch(() => props.graphData, (newData) => {
            // 若外部传入更新，我们仍用 setData 重建
            if (networkInstance) {
                const newEdges = newData.edges.map(e => ({
                    ...e,
                    length: e.weight ? 200 / e.weight : 200,
                    color: { color: '#2B7CE9' }
                }))
                const newNodes = newData.nodes.map(n => ({
                    ...n,
                    color: {
                        background: getColorByType(n.type),
                        border: '#2B7CE9'
                    }
                }))
                networkInstance.setData({ nodes: newNodes, edges: newEdges })
            }
        }, { deep: true })

        return {
            networkContainer,
            selectedInfo
        }
    }
}
</script>

<style scoped>
.graph-container {
    width: 100%;
    height: 400px;
    border-bottom: var(--border);
    /* border-radius: 12px; */
    /* margin-bottom: 12px; */
}

.info-panel {
    /* background: #f9f9f9; */
    padding: 16px;
    border: 1px solid #ddd;
    /* border-radius: 8px; */
    height: 294px;
    overflow-y: auto;
}
</style>