<template>

    <div class="todo-content" @click="jumpWithId">
        <div class="mytitle">{{ type }}</div>
        <div class="wrapper2">
            <div style="flex: 1 1 0%;">处理时间：{{ date }}</div>
            <div style="flex: 6 1 0%;">{{ type === '综述生成' ? '综述领域' : '论文题目' }}：{{ para3 }}
            </div>
        </div>
    </div>

</template>

<script>


export default {
    components: {

    },
    props: [
        'type', 'date', 'para3', 'id'
    ],
    data() {
        return {
            // date: '',
        }
    },
    methods: {
        jumpWithId() {
            this.$router.push(`generate?id=${this.id}`)
        }
    },
    mounted() {
        console.log(this.date);

    }
}
</script>


<style scoped>
.todo-content {
    border: var(--border);
    max-width: 500px;
}

.mytitle {
    flex: 2;
    text-shadow: none;
    font-size: large;
    font-weight: bold;
}
</style>