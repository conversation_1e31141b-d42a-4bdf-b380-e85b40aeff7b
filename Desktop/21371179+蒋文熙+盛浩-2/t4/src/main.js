import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from 'axios'



import '@/assets/css/style.css'
import '@/assets/css/style.scss'
import '@/assets/font/bootstrap-icons.css'
// main.js 或 main.ts 中
// import 'vue3-datepicker/dist/style.css'

import '@/assets/js/v1.js'
import request from '@/utils/api.js'


const app = createApp(App).use(store).use(router)
app.config.globalProperties.$http=axios;
app.mount('#app')