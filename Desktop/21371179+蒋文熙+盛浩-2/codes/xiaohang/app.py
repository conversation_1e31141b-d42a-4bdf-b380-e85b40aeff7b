from fastapi import Fast<PERSON><PERSON>, Request, Header, HTTPException
from pydantic import BaseModel
from typing import List, Optional, AsyncGenerator
import time
import uuid
import requests
import json
from fastapi.responses import StreamingResponse
import aiohttp

app = FastAPI()

# 定义请求格式
class Message(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    model: str
    messages: List[Message]
    temperature: Optional[float] = 1.0
    max_tokens: Optional[int] = 1024
    stream: bool = False

# 转发chat请求！
@app.post("/v1/chat/completions")
async def chat_completions(
    req: ChatRequest,
    authorization: str = Header(default=None)
):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid API key")

    api_key = authorization.split(" ")[1]
    print(f"API Key is: {api_key}")
    messages = req.messages
    print(f'message: {messages}')
    system_prompt = next((m.content for m in messages if m.role == "system"), "")
    user_prompt = next((m.content for m in reversed(messages) if m.role == "user"), "")
    payload = {
        "stream": req.stream,
        "metadata": {
            "name": "小航",
            "developer": "北京航空航天大学"
        },
        "messages": [{"role": m.role, "content": m.content} for m in messages]
    }
    headers = {
        'x-api-key': api_key,
        'Content-Type': 'application/json'
    }

    if req.stream:
        async def event_generator() -> AsyncGenerator[str, None]:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://api.xhang.buaa.edu.cn:28119/apps/llm/chat/agent",
                    json=payload,
                    headers=headers,
                    ssl=False,
                ) as response:
                    async for line in response.content:
                        line = line.decode("utf-8").strip()
                        if line.startswith("data:"):
                            event_data = line[5:].strip()

                            if event_data == "[DONE]":
                                yield "data: " + json.dumps({
                                        "id": f"chatcmpl-{str(uuid.uuid4())[:8]}",
                                        "object": "chat.completion.chunk",
                                        "created": int(time.time()),
                                        "model": "",
                                        "service_tier": 'default',
                                        "choices": [{
                                            "delta": {  },
                                            "index": 0,
                                            "finish_reason": "stop",
                                            "logprobs": None
                                        }]
                                    }, ensure_ascii=False) + "\n\n"

                                yield "data: [DONE]\n\n"
                            else:
                                try:
                                    parsed = json.loads(event_data)
                                    content = parsed["choices"][0]["message"]["content"]
                                    print(content, flush=True, end='')
                                    if not parsed["choices"][0]["finish_reason"]:
                                        yield "data: " + json.dumps({
                                            "id": f"chatcmpl-{str(uuid.uuid4())[:8]}",
                                            "object": "chat.completion.chunk",
                                            "created": int(time.time()),
                                            "model": "",
                                            "service_tier": 'default',
                                            "choices": [{
                                                "delta": {
                                                    "role": "assistant",
                                                    "content": content,
                                                    "function_call": None,
                                                    },
                                                "index": 0,
                                                "finish_reason": None,
                                                "logprobs": None
                                            }]
                                        }, ensure_ascii=False) + "\n\n"
                                except Exception as e:
                                    print("解析失败：", e)
                                    continue

        return StreamingResponse(event_generator(), media_type="text/event-stream")
    else:
        response = requests.post("https://api.xhang.buaa.edu.cn:28119/apps/llm/chat/agent?test_harmful=true",
                                headers=headers,
                                data=json.dumps(payload))
        result = response.json()
        print(f'respons: {response.text}')
        reply_content = result["choices"][0]["message"]["content"]
        print(f'content: {reply_content}')
        # 构造 OpenAI 风格响应
        return {
            "id": f"chatcmpl-{str(uuid.uuid4())[:8]}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": reply_content
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": result.get("usage", {}).get("prompt_tokens", 0),
                "completion_tokens": result.get("usage", {}).get("completion_tokens", 0),
                "total_tokens": result.get("usage", {}).get("total_tokens", 0)
            }
        }