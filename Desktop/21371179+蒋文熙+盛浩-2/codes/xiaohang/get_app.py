import requests

url = "http://0.0.0.0:8001/v1/chat/completions"
   
api_key=''
bib = '''
@ARTICLE{2024arXiv241118688S,
       author = {{<PERSON><PERSON><PERSON>}, <PERSON><PERSON><PERSON> and {Chakrabor<PERSON>}, <PERSON><PERSON><PERSON><PERSON> and {<PERSON>}, <PERSON><PERSON><PERSON><PERSON> and {<PERSON><PERSON>}, <PERSON><PERSON><PERSON><PERSON> and {<PERSON>}, <PERSON><PERSON><PERSON> and {<PERSON><PERSON><PERSON>}, <PERSON> and {<PERSON>}, <PERSON><PERSON> and {<PERSON><PERSON><PERSON><PERSON>}, <PERSON><PERSON> and {<PERSON><PERSON><PERSON>}, <PERSON><PERSON> and {<PERSON>}, Amrit},
        title = "{Immune: Improving Safety Against Jailbreaks in Multi-modal LLMs via Inference-Time Alignment}",
      journal = {arXiv e-prints},
     keywords = {Computer Science - Cryptography and Security, Computer Science - Artificial Intelligence, Computer Science - Machine Learning},
         year = 2024,
        month = nov,
          eid = {arXiv:2411.18688},
        pages = {arXiv:2411.18688},
          doi = {10.48550/arXiv.2411.18688},
archivePrefix = {arXiv},
       eprint = {2411.18688},
 primaryClass = {cs.CR},
       adsurl = {https://ui.adsabs.harvard.edu/abs/2024arXiv241118688S},
      adsnote = {Provided by the SAO/NASA Astrophysics Data System}
}
'''

example = """@ARTICLE{2025arXiv250113776K,
       author = {{Kummer}, Lorenz and {Moustafa}, Samir and {Gansterer}, Wilfried and {Kriege}, Nils},
        title = "{Crossfire: An Elastic Defense Framework for Graph Neural Networks Under Bit Flip Attacks}",
      journal = {arXiv e-prints},
     keywords = {Computer Science - Machine Learning},
         year = 2025,
        month = jan,
          eid = {arXiv:2501.13776},
        pages = {arXiv:2501.13776},
          doi = {10.48550/arXiv.2501.13776},
archivePrefix = {arXiv},
       eprint = {2501.13776},
 primaryClass = {cs.LG},
       adsurl = {https://ui.adsabs.harvard.edu/abs/2025arXiv250113776K},
      adsnote = {Provided by the SAO/NASA Astrophysics Data System}
}

"""
prompt = f"把以下信息改写成gb的引用格式，保留英文名称：{bib},除了引用外不需要输出任何其他信息，示例：\
                输入：{example}\
                输出："

payload = {
    "model": "gpt-3.5-turbo", 
    "messages": [
        {
            "role": "user",
            "content": "给我讲个笑话",
        }
    ],
    "stream": True 
}

headers = {
    "accept": "application/json",
    "content-type": "application/json",
    "authorization": f"Bearer {api_key}"
}
   
response = requests.post(url, json=payload, headers=headers, stream=True) # 此处request需要指定stream模式

# 打印流式返回信息
if response.status_code == 200: 
    for chunk in response.iter_content(chunk_size=8192): 
        if chunk:
            decoded_chunk = chunk.decode('utf-8')
            print(decoded_chunk, end='')
else:
    print('Request failed with status code:', response.status_code)