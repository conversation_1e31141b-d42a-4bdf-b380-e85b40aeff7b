from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request, UploadFile, Query
from bs4 import BeautifulSoup, NavigableString, Tag
from starlette.datastructures import UploadFile as StarletteFile

from typing import Dict, Any
import requests
import re
import datetime
import json
import time
import os
import asyncio
from pathlib import Path
from datetime import date


from generate.utils import read_pdf, extract_graph, get_bib, bib2item

import uuid


from generate import index, read


app = FastAPI()

def parse_search_text(tag):
    string = ""
    for child in tag.children:
        if isinstance(child, NavigableString):
            string += re.sub(r"\s+", " ", child)
        elif isinstance(child, Tag):
            if child.name == "span" and "search-hit" in child.get("class"):
                string += re.sub(r"\s+", " ", child.get_text(strip=False))
            elif child.name == "a" and ".style.display" in child.get("onclick"):
                pass
            else:
                import pdb

                pdb.set_trace()
    return string

def checkCache(id):
    folder_path = f'{str(os.curdir)}/cache/{id}'
    if os.path.exists(f'{folder_path}/content'):
        content = open(f'{folder_path}/content', 'r').read()
        nodes = json.load(open(f'{folder_path}/nodes.json', 'r'))
        edges = json.load(open(f'{folder_path}/edges.json', 'r'))
        return content, nodes, edges
    else:
        Path(folder_path).mkdir(parents=True, exist_ok=True)
        return None
    
def getPaperBin(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')

    link_tag = soup.find('a', class_='abs-button abs-button-small cite-ads')  
    if link_tag:
        link = link_tag.get('href')  



@app.get('/searchTitle')
def searchTitle(name: str, page: int):
    title_string = name.replace(' ', "+")
    searchUrl = f'https://arxiv.org/search/?query={title_string}&searchtype=all&abstracts=show&order=-announced_date_first&size=50&start={page*50}'
    print(searchUrl)

    response = requests.get(searchUrl)
    content = response.text

    data = []
    try:
        soup = BeautifulSoup(content, "html.parser")
        results = soup.find_all("li", {"class": "arxiv-result"})
        for result in results:
            url_tag = result.find("a")
            url = url_tag["href"] if url_tag else "No link"

            title_tag = result.find("p", class_="title")
            title = parse_search_text(title_tag) if title_tag else "No title"
            title = title.strip()

            date_tag = result.find("p", class_="is-size-7")
            date = date_tag.get_text(strip=True) if date_tag else "No date"
            if "v1" in date:
                # Submitted9 August, 2024; v1submitted 8 August, 2024; originally announced August 2024.
                # 注意空格会被吞掉，这里我们要找最早的提交日期
                v1 = date.find("v1submitted")
                date = date[v1 + 12 : date.find(";", v1)]
            else:
                # Submitted8 August, 2024; originally announced August 2024.
                # 注意空格会被吞掉
                submit_date = date.find("Submitted")
                date = date[submit_date + 9 : date.find(";", submit_date)]

            authors_tag = result.find("p", class_="authors")
            authors = authors_tag.get_text(strip=True)[len("Authors: <AUTHORS>

            data.append({
                "title": title,
                "date": datetime.datetime.strptime(date, "%d %B, %Y").date(),
                "authors": authors,
                "url": url.replace('/abs/', '/pdf/'),
                "chose": False,
                "type": "arxiv",
            })
            
    except:
        pass

    return data

@app.post('/uploadFile')
async def getFiles(request: Request):
    type = request.query_params['type']
    if type == 'survey':
        field = request.query_params['field']
        paper_list = []
        if type == 'survey':
            paper_list.append({
                'filed': field
            })

    unique_id = str(uuid.uuid4()).replace('-', '')

    form = await request.form()
    json_data = json.loads(form.get('json'))

    cur_path = os.curdir
    Path(f'{cur_path}/temp/{unique_id}/input').mkdir(parents=True, exist_ok=True)
    Path(f'{cur_path}/temp/{unique_id}/generations').mkdir(parents=True, exist_ok=True)
    with open(f'{cur_path}/temp/{unique_id}/type', 'w') as file:
        file.write(type)

    title = ""


    for (index, item) in enumerate(json_data['item']):
        if item['type'] == 'arxiv':
            url:str = item['url']
            title = item['title']
            text = await read_pdf.read_pdf_url(url)
            if type == 'survey':
                bib = await get_bib.get_bib(url.replace('pdf', 'abs'))
                paper_list.append({
                    'has_bib': True,
                    'bib': bib,
                    'title': item['title']
                })
        else:
            title = item['title']
            name = f'file_{index}'
            file = form.get(name)
            text = read_pdf.read_pdf_upload(file)
            if type == 'survey':
                paper_list.append({
                    'has_bib': False,
                    'title': item['title']
                })
        
        with open(f'{cur_path}/temp/{unique_id}/input/{index}.txt', 'w') as file:
            file.write(text)

    if type == 'survey':
        with open(f'{cur_path}/temp/{unique_id}/paper_list.json', 'w') as file:
            json.dump(paper_list, file)
    else:
        with open(f'{cur_path}/temp/{unique_id}/title', 'w') as file:
            file.write(title)

    return {
        'id': unique_id
    }


@app.get('/getoutput')
async def getOutput(id):
    result = checkCache(id)    
    time.sleep(20)
    if result:
        content, nodes, edges = result
        graph = {
            'nodes' : nodes,
            'edges' : edges
        }
        return {
            'content': content,
            'graph': graph
        }
    else:
        today = date.today()
        formatted_date = today.strftime('%Y-%m-%d')
        cur_path = os.curdir
        with open(f'{cur_path}/temp/{id}/type') as file:
            type = file.read()

        if type == 'detail':
            with open(f'{cur_path}/temp/{id}/title') as file:
                title = file.read()
            await index.run_index(id)
            content = await read.gen_detail(id)
            dd = {
                'date': formatted_date,
                'type': '要点分析',
                'title': title,
                'id': id
            }
        elif type == 'rough':
            await index.run_index(id)
            with open(f'{cur_path}/temp/{id}/title') as file:
                title = file.read()
            content = await read.gen_rough(id)
            dd = {
                'date': formatted_date,
                'type': '论文总结',
                'title': title,
                'id': id
            }
        else: 
            # task_index = asyncio.create_task(index.run_index(id))
            # await task_index
            # await index.run_index(id)

            with open(f'{cur_path}/temp/{id}/paper_list.json', 'r') as file:
                data = json.load(file)
            field = data[0]['filed']

            ref_list = []

            print(field)

            for paper in data[1:]:
                if paper['has_bib']:
                    # task_getBin = asyncio.create_task(bib2item.get_item(paper['bib']))
                    # ref = await task_getBin
                    ref = bib2item.get_item(paper['bib'])
                    ref_list.append(ref)


            print(ref_list)
            content = ""
            content = await read.gen_survey(id, field)
            # print(content)
            #
            content = f'{content}\n{"\n".join(ref_list)}'
            with open(f'{cur_path}/cache/{id}/content', 'w') as file:
                file.write(content)

            dd = {
                'date': formatted_date,
                'type': '综述生成',
                'title': field,
                'id': id
            }

        with open(f'{cur_path}/cache/{id}/history.json', 'w') as file:
            json.dump(dd, file, ensure_ascii=False)
        graph = extract_graph.get_graph(id)
        return {
            'content': content,
            'graph': graph
        }
    
@app.get('/gethistory')
async def getOutput():
    ret = []
    path = './cache'
    for name in os.listdir(path):
        full_path = os.path.join(path, name)
        if os.path.isdir(full_path):
            print(name)
            with open(f'{full_path}/history.json', 'r') as file:
                ret.append(json.load(file))
    # return [
    #     {
    #         'type': '综述生成',
    #         'date': '2025-05-01',
    #         'title': 'Evaluating Knowledge Editing',
    #         'id': '272141e3bb404528a1386339b173129c'
    #     },
    #     {
    #         'type': '要点分析',
    #         'date': '2025-04-28',
    #         'title': '3792_AlphaEdit_Null_Space_Cons',
    #         'id': '5282c168ee1646ccbffd6112347f0c28'
    #     }
    # ]

    return ret

def check_a(paper_url):
    a_list = ['tocs','tos','tcad','tc','tpds','taco','ppopp','fast','dac','hpca','micro','sc','asplos','isca','usenix','eurosys','jsac','tmc','ton','sigcomm','mobicom','infocom','nsdi','tdsc','tifs','ccs','eurocrypt','s&p','crypto','ndss','toplas','tosem','tse','tsc','pldi','popl','fse','sosp','oopsla','ase','icse','issta','osdi','fm','tods','tois','tkde','vldbj','sigmod','sigkdd','icde','sigir','vldb','tit','iandc','sicomp','stoc','soda','cav','focs','lics','tog','tip','tvcg','acm-mm','siggraph','vr','ieee-vis','ai','tpami','ijcv','jmlr','aaai','neurips','acl','cvpr','iccv','icml','ijcai','tochi','ijhcs','cscw','chi','ubicomp','uist','jacm','proc.-ieee','scis','www','rtss wine']
    content = requests.get(paper_url).text.lower()
    pattern = r'\b(?:' + '|'.join(re.escape(word.lower()) for word in a_list) + r')\b'
    matches = re.findall(pattern, content)
    if matches: 
        return True
    return False

@app.post('/searchAdvance')
async def advance(request: Request):
    json_data = await request.json()
    page = request.query_params.get('page')
    print(page)

    title = request.query_params.get('name')

    tags = json_data['tags']
    authors = json_data['authors']
    startDate = json_data['startDate'][:10] if json_data['startDate'] else None
    endDate = json_data['endDate'][:10] if json_data['endDate'] else None
    isEnabled = json_data['isEnabled']

    url = f'https://arxiv.org/search/advanced?advanced=&terms-0-operator=AND&terms-0-term={title}&terms-0-field=title'
    cnt = 1
    for author in authors:
        key = f'&terms-{cnt}-operator=AND&terms-{cnt}-term={author}&terms-{cnt}-field=author'
        url = url + key
        cnt += 1
    for tag in tags:
        key = f'&terms-{cnt}-operator=AND&terms-{cnt}-term={tag}&terms-{cnt}-field=all'
        url = url + key
        cnt += 1
    if endDate and startDate:
        url += f'date-filter_by=date_range&date-from_date={startDate}&date-to_date={endDate}&date-date_type=announced_date_first'

    print(url)
    response = requests.get(url)
    content = response.text



    data = []
    try:
        soup = BeautifulSoup(content, "html.parser")
        results = soup.find_all("li", {"class": "arxiv-result"})
        for result in results:

            url_tag = result.find("a")
            url = url_tag["href"] if url_tag else "No link"

            if isEnabled:
                if not check_a(url):
                    raise Exception

            title_tag = result.find("p", class_="title")
            title = parse_search_text(title_tag) if title_tag else "No title"
            title = title.strip()

            date_tag = result.find("p", class_="is-size-7")
            date = date_tag.get_text(strip=True) if date_tag else "No date"
            if "v1" in date:
                # Submitted9 August, 2024; v1submitted 8 August, 2024; originally announced August 2024.
                # 注意空格会被吞掉，这里我们要找最早的提交日期
                v1 = date.find("v1submitted")
                date = date[v1 + 12 : date.find(";", v1)]
            else:
                # Submitted8 August, 2024; originally announced August 2024.
                # 注意空格会被吞掉
                submit_date = date.find("Submitted")
                date = date[submit_date + 9 : date.find(";", submit_date)]

            authors_tag = result.find("p", class_="authors")
            authors = authors_tag.get_text(strip=True)[len("Authors: <AUTHORS>

            data.append({
                "title": title,
                "date": datetime.datetime.strptime(date, "%d %B, %Y").date(),
                "authors": authors,
                "url": url.replace('/abs/', '/pdf/'),
                "chose": False,
                "type": "arxiv",
            })
            

    except:
        pass

    return(data)

