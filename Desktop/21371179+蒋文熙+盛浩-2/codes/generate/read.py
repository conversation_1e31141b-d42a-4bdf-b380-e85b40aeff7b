import os
import asyncio
import re

from generate.utils.load_db import load_tables, load_embeds
from generate.utils.engines import search_loacl, search_global

from generate.utils.chat_llm import rough_summary, translate
from generate.utils.get_index import get_indexs


def write2file(filename, content):
    with open(filename, 'w') as file:
        file.write(content)


def cut(str):
    pattern = r'\[Data: [A-Z][a-z]* \(\d*\)(; [A-Z][a-z]* \(\d*\))*\]'

    matches = list(re.finditer(pattern, str))

    removed_parts = [match.group(0) for match in matches]

    new_text = re.sub(pattern, '', str)

    match_positions = [match.start() for match in matches]
    
    return new_text, removed_parts, str


async def process4read(id: str):

    cur = os.curdir
    workdir = f'{cur}/temp/{id}'


    entities, relationships, reports, text_units, communities = load_tables(workdir)
    description_embedding_store, _ = load_embeds(workdir)

    api_key = os.environ["GRAPHRAG_API_KEY"]
    llm_model = os.environ["GRAPHRAG_LLM_MODEL"]
    embedding_model = os.environ["GRAPHRAG_EMBEDDING_MODEL"]
    print(api_key)
    print(llm_model)


    search_engine = search_loacl(api_key=api_key, llm_model=llm_model, embedding_model=embedding_model, reports=reports, relationships=relationships, entities=entities, text_units=text_units, description_embedding_store=description_embedding_store)

    print('search_engine initial')


    result = await (search_engine.search("What is the research background and the inspiration of this paper?"))
    background, _, _ = cut(result.response)
    write2file(f'{workdir}/generations/background.txt', background)


    result = await (search_engine.search("How is the research method proposed in this paper implemented?"))
    method, _, _ = cut(result.response)
    write2file(f'{workdir}/generations/method.txt', method)

    result = await (search_engine.search("Did this paper include any experiments? If so, how were the experiments conducted, and what were the results?"))
    experiment, _, _ = cut(result.response)
    write2file(f'{workdir}/generations/experiment.txt', experiment)



async def gen_rough(id: str):
    await process4read(id)
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'
    try:
        with open(f'{workdir}/generations/background.txt', 'r') as file:
            background = file.read()
    except FileNotFoundError:
        pass
    try:
        with open(f'{workdir}/generations/method.txt', 'r') as file:
            method = file.read()
    except FileNotFoundError:
        pass
    try:
        with open(f'{workdir}/generations/experiment.txt', 'r') as file:
            experiment = file.read()
    except FileNotFoundError:
        pass
    content = rough_summary(cut(background)[0], cut(method)[0], cut(experiment)[0])

    with open(f'{cur}/cache/{id}/content', 'w') as file:
        file.write(content)
        
    return content


async def gen_detail(id: str) -> str:
    await process4read(id)
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'
    try:
        with open(f'{workdir}/generations/background.txt', 'r') as file:
            background = file.read()
    except:
        print('aaa')
        pass

    try:
        with open(f'{workdir}/generations/method.txt', 'r') as file:
            method = file.read()
    except FileNotFoundError:
        pass
    try:
        with open(f'{workdir}/generations/experiment.txt', 'r') as file:
            experiment = file.read()
    except FileNotFoundError:
        pass
    content = translate(cut(f'{background}\n\n{method}\n\n{experiment}')[0])

    with open(f'{cur}/cache/{id}/content', 'w') as file:
        file.write(content)

    return content


async def sources():
    pass

def replace_ref(raw:str, target, removes):
    for index, item in enumerate(removes):
        t = target[index]
        raw = raw.replace(item, t, 1)
    return raw

async def process4write(id:str, field:str):
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'
    entities, relationships, reports, text_units, communities = load_tables(workdir)
    description_embedding_store, full_content_embedding_store = load_embeds(workdir)
    api_key = os.environ["GRAPHRAG_API_KEY"]
    llm_model = os.environ["GRAPHRAG_LLM_MODEL"]
    embedding_model = os.environ["GRAPHRAG_EMBEDDING_MODEL"]

    api_key = os.environ["GRAPHRAG_API_KEY"]
    llm_model = os.environ["GRAPHRAG_LLM_MODEL"]
    embedding_model = os.environ["GRAPHRAG_EMBEDDING_MODEL"]

    search_engine = search_global(api_key, llm_model, reports, entities, communities)

    prompt = f"""Write a literature review on the topic of {field}. The review should include the following components:
	1.	Introduction – Provide background information and clearly state the motivation for the research.
	2.	Definition of the Research Area – Define the scope of the review and specify the core issues it addresses.
	3.	Systematic Summary and Comparison of Existing Research – Offer a comprehensive and structured overview of the relevant literature, comparing different studies and highlighting their key contributions.
	4.	Challenges and Future Directions (if applicable) – Discuss any remaining challenges in the field and potential directions for future research.
Please ensure that the writing is logically structured and academically rigorous. Maintain a clear and coherent organization throughout the review. Do not omit the core contributions of each paper, and aim to uncover the underlying connections between them.
"""
    
    result = await search_engine.search(prompt)
    print(result.response)
    content, removed_parts, raw = cut(result.response)
    write2file(f'{workdir}/generations/content.txt', content)

    indexs = get_indexs(id, removed_parts)
    content = replace_ref(raw, removed_parts, indexs)

    return content



async def gen_survey(id: str, field: str) -> str:
    return await process4write(id, field)[0]


def read_history():
    try:
        with open('background.txt', 'r') as file:
            background = file.read()
    except FileNotFoundError:
        pass
    try:
        with open('method.txt', 'r') as file:
            method = file.read()
    except FileNotFoundError:
        pass
    try:
        with open('experiment.txt', 'r') as file:
            experiment = file.read()
    except FileNotFoundError:
        pass
    content = translate(cut(f'{background}\n\n{method}\n\n{experiment}')[0])
    write2file('detailed_summary.txt', content)

