import os
import asyncio
import re
import pandas as pd

from generate.utils.load_db import load_tables, load_embeds
from generate.utils.engines import search_loacl, search_global

from generate.utils.chat_llm import rough_summary, translate, chat




def write2file(filename, content):
    with open(filename, 'w') as file:
        file.write(content)


def cut(str):
    pattern = r'\[Data: [A-Z][a-z]* \((\d+(, \d+)*)\)(; [A-Z][a-z]* \((\d+(, \d+)*)\))*\]'
    matches = list(re.finditer(pattern, str))
    
    removed_parts = [match.group(0) for match in matches]
    
    new_text = re.sub(pattern, '', str)
    
    return new_text, removed_parts, str

def get_source_id(str):
    match = re.search(r"Sources \(([\d,]+)\)", str)
    if match:
        number_list = [int(n) for n in match.group(1).split(',')]
        print(number_list)  # 输出: [10, 203, 5]
        return number_list
    return []


def get_title_by_sourceid(id, source_id:int) :
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'
    unit_path = f'{workdir}/output/text_units.parquet'
    df = pd.read_parquet(unit_path)
    filtered = df[df['human_readable_id'] == source_id]

    doc_path = f'{workdir}/output/documents.parquet'
    doc_id = filtered['document_ids'].iloc[0][0]

    doc_df = pd.read_parquet(doc_path)
    filtered = doc_df[doc_df['id'] == doc_id]
    title = filtered['title'].iloc[0]
    
    filtered = doc_df[doc_df['id'] == doc_id]
    doc = filtered['human_readable_id'].iloc[0]
    return title, str(int(doc))


def title2bib(title:str):
    return f"{title}xxxxxxxxxxxx"


def get_indexs(id:str, removed_parts:list[str]):

    new_str = []
    refs = []

    for item in removed_parts:
        number_list = get_source_id(item)
        index_list = []
        for num in number_list:
            title, doc_index = get_title_by_sourceid('272141e3bb404528a1386339b173129c', num)
            index_list.append(doc_index)
        new_str.append(f"[{','.join(index_list)}]")
        if doc_index in refs:
            refs.append("")
        else:
            refs.append(doc_index)
    return refs
    
    
