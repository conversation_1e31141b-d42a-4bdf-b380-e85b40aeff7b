import fitz  # PyMuPDF
import re
import aiohttp


def remove_after_last_ref1(text, str1):
    pattern = f"(.*){re.escape(str1)}.*"
    match = re.match(pattern, text, re.IGNORECASE)
    if match:
        return True, match.group(1)

def remove_after_last_Ref(text, str1):
    pattern = f"(.*){re.escape(str1)}.*"
    match = re.match(pattern, text, re.DOTALL)
    if match:
        return True, match.group(1)
    return False, text  

def read_pdf_text(doc):
    text = ""
    for page in doc:
        text += page.get_text("text")
    full_text = "".join(text.splitlines())
    return full_text



async def read_pdf_url(pdf_url):
    async with aiohttp.ClientSession(trust_env=True) as client:
        response = await client.get(pdf_url, ssl=False)
        response.raise_for_status()
        content = await response.read()
        doc = fitz.open(stream=content, filetype="pdf")
        full = read_pdf_text(doc)
        flag, full1 =  remove_after_last_ref1(full, "references[1]")
        if flag: 
            return full1
        flag, full2 = remove_after_last_Ref(full1, "References")
        return full2


def read_pdf_upload(f):
    bytes = f.file.read()
    doc = fitz.open(stream=bytes, filetype='pdf')
    full = read_pdf_text(doc)
    flag, full1 =  remove_after_last_ref1(full, "references[1]")
    if flag: 
        return full1
    flag, full2 = remove_after_last_Ref(full1, "References")
    return full2



