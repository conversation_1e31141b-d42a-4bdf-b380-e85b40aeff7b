import requests
from bs4 import BeautifulSoup
import aiohttp
import asyncio

import generate.utils.chat_llm as chat


async def get_bib(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as res:
            soup = BeautifulSoup(await res.text(), 'html.parser')

            link_tag = soup.find('a', class_='abs-button abs-button-small cite-ads')

            if link_tag:
                url = link_tag.get('href')
            else:
                return None

        async with session.get(url, allow_redirects=False) as response:
            if 300 <= response.status < 400:
                redirect_url = response.headers.get('Location')
                new_url = redirect_url.replace('abstract', 'exportcitation')

                async with session.get(new_url) as redirected_response:
                    text = await redirected_response.text()
                    soup = BeautifulSoup(text, 'html.parser')
                    content = soup.find('textarea', class_='export-textarea form-control').text
                    # print(content)
                    return content
            else:
                print("没有重定向，状态码：", response.status)
                return None


def get_ref(bib):
    prompt = f'''Convert BibTeX entries into citation content according to the specified format. Do not include numbering or any extra output.
Format:
	1.	Journal article: Main author. Title of the article [J]. Journal Name, Year of Publication, Volume(Issue): Page numbers.
	2.	Book: Main author. Title of the book [M]. Place of Publication: Publisher, Year: Page numbers.
	3.	Conference paper: Main author. Title of the paper [A] // Editor. Title of the conference proceedings [C]. Place of Publication: Publisher, Year: Page numbers.
	4.	Thesis or dissertation: Main author. Title of the thesis [D]. Location: Institution, Year.
	5.	Report: Main author. Title of the report [R]. Place of Report: Organizer, Year.
	6.	Patent: Patent holder. Title of the patent [P]. Country: Patent number, Date of issue.
	7.	Standard: Standard number, Standard title [S]. Place of Publication: Publisher, Year.
	8.	Newspaper article: Author. Title of the article [N]. Name of Newspaper, Date (Edition).
	9.	Electronic document: Author. Title of the electronic document [Document Type/Media Type]. Available at: Address.
bibtex：
{bib}
---example---
input: 
@inproceedings{{
      liu2024autodan,
      title={{AutoDAN: Generating Stealthy Jailbreak Prompts on Aligned Large Language Models}},
      author={{Xiaogeng Liu and Nan Xu and Muhao Chen and Chaowei Xiao}},
      booktitle={{The Twelfth International Conference on Learning Representations}},
      year={{2024}},
      url={{https://openreview.net/forum?id=7Jwpw4qKkb}}
}}
output:
Liu Xiaogeng, Xu Nan, Chen Muhao, Xiao Chaowei. AutoDAN: Generating Stealthy Jailbreak Prompts on Aligned Large Language Models[A]//Proceedings of The Twelfth International Conference on Learning Representations[C]. 2024.
---example end---

output：
'''
    
    output = chat.chat(prompt)
    return output