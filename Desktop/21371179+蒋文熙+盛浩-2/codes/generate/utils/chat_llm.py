import openai# 设置你的API密钥
import os

api_key = os.environ["GRAPHRAG_API_KEY"]

def file2txt(filename):
    with open(filename, 'r') as file:
        content = file.read()
    return content

def write2file(filename, content):
    with open(filename, 'w') as file:
        file.write(content)


def translate(text, target_language='Chinese'):    
    client = openai.OpenAI(api_key=api_key)

    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": f"Translate the following text to {target_language} while keeping the original format; do not output anything other than the translated text: {text}"
            }
        ]
    )

    # print(completion.choices[0].message.content)
    return completion.choices[0].message.content



def rough_summary(background, method, experiment):
    client = openai.OpenAI(api_key=api_key)
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": f"Based on the following information, generate a summary that enables users to quickly and accurately grasp the key content of the paper.\n The background and the inspiration of this paper can be summarized as {background}\n The research method proposed in this paper can be summarized as {method}\n The experiment and results of this paper can be summarized as {experiment}. Summary: "
            }
        ]
    )


    content = completion.choices[0].message.content
    return translate(content)

def chat(query):    
    client = openai.OpenAI(api_key=api_key)

    history = [
        {
            "role": "user",
            "content": query
        }
    ]

    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=history,
    )

    print(response.choices[0].message.content)
    return response.choices[0].message.content


