import os
import pandas as pd
import json

def get_graph(id):
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'

    
    entities_df = pd.read_parquet(f'{workdir}/output/entities.parquet')
    relationships_df = pd.read_parquet(f'{workdir}/output/relationships.parquet')

    entities_fields = ['title', 'type', 'description']
    relationships_fields = ['source', 'target', 'description', 'weight']
    E_selected = entities_df[entities_fields]
    R_selected = relationships_df[relationships_fields]

    E_data = E_selected.to_dict(orient='records')
    R_data = R_selected.to_dict(orient='records')

    title2id = {}
    nodes = []
    for (index, value) in enumerate(E_data):
        value['id'] = index
        title2id[value['title']] = index
        nodes.append({
            'id': index,
            'label': value['title'],
            'type': value['type'],
            'detail': value['description']
        })


    edges = []
    for (index, value) in enumerate(R_data):
        edges.append({
            'id': f'e{index}',
            'detail': value['description'],
            'from': title2id[value['source']],
            'to': title2id[value['target']],
            'weight': int(value['weight']),
            'label': ""
        })


    with open(f'./cache/{id}/nodes.json', 'w') as file:
        json.dump(nodes, file)
    with open(f'./cache/{id}/edges.json', 'w') as file:
        json.dump(edges, file)


    return {
        'nodes' : nodes,
        'edges' : edges
    }
