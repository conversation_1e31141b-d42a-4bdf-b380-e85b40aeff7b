import tiktoken


from graphrag.query.context_builder.entity_extraction import <PERSON><PERSON>tyVectorStoreK<PERSON>

from graphrag.query.question_gen.local_gen import LocalQuestionGen
from graphrag.query.structured_search.local_search.mixed_context import (
    LocalSearchMixedContext,
)
from graphrag.query.structured_search.local_search.search import LocalSearch
from graphrag.config.models.drift_search_config import DRIFTSearchConfig

from graphrag.config.enums import ModelType
from graphrag.config.models.language_model_config import LanguageModelConfig
from graphrag.language_model.manager import ModelManager
from graphrag.query.structured_search.drift_search.drift_context import (
    DRIFTSearchContextBuilder,
)
from graphrag.query.structured_search.drift_search.search import DRIFTSearch
from graphrag.query.structured_search.global_search.community_context import (
    GlobalCommunityContext,
)
from graphrag.query.structured_search.global_search.search import GlobalSearch



def search_loacl(api_key, llm_model, embedding_model, reports, text_units, entities, relationships, description_embedding_store):
    chat_config = LanguageModelConfig(
        api_key=api_key,
        type=ModelType.OpenAIChat,
        model=llm_model,
        max_retries=20,
    )

    chat_model = ModelManager().get_or_create_chat_model(
        name="local_search",
        model_type=ModelType.OpenAIChat,
        config=chat_config,
    )

    token_encoder = tiktoken.encoding_for_model(llm_model)

    embedding_config = LanguageModelConfig(
        api_key=api_key,
        type=ModelType.OpenAIEmbedding,
        model=embedding_model,
        max_retries=20,
    )

    text_embedder = ModelManager().get_or_create_embedding_model(
        name="local_search_embedding",
        model_type=ModelType.OpenAIEmbedding,
        config=embedding_config,
    )


    context_builder = LocalSearchMixedContext(
        community_reports=reports,
        text_units=text_units,
        entities=entities,
        relationships=relationships,
        entity_text_embeddings=description_embedding_store,
        embedding_vectorstore_key=EntityVectorStoreKey.ID,  
        text_embedder=text_embedder,
        token_encoder=token_encoder,
    )

    local_context_params = {
        "text_unit_prop": 0.5,
        "community_prop": 0.1,
        "conversation_history_max_turns": 5,
        "conversation_history_user_turns_only": True,
        "top_k_mapped_entities": 10,
        "top_k_relationships": 10,
        "include_entity_rank": True,
        "include_relationship_weight": True,
        "include_community_rank": False,
        "return_candidate_context": False,
        "embedding_vectorstore_key": EntityVectorStoreKey.ID, 
        "max_tokens": 12_000,  
    }

    model_params = {
        "max_tokens": 2_000, 
        "temperature": 0.0,
    }

    search_engine = LocalSearch(
        model=chat_model,
        context_builder=context_builder,
        token_encoder=token_encoder,
        model_params=model_params,
        context_builder_params=local_context_params,
        response_type="multiple paragraphs", 
    )

    return search_engine






def search_global(api_key, llm_model, reports, entities, communities):
    config = LanguageModelConfig(
        api_key=api_key,
        type=ModelType.OpenAIChat,
        model=llm_model,
        max_retries=20,
    )
    model = ModelManager().get_or_create_chat_model(
        name="global_search",
        model_type=ModelType.OpenAIChat,
        config=config,
    )

    token_encoder = tiktoken.encoding_for_model(llm_model)

    context_builder = GlobalCommunityContext(
        community_reports=reports,
        communities=communities,
        entities=entities,  
        token_encoder=token_encoder,
    )


    context_builder_params = {
        "use_community_summary": False, 
        "shuffle_data": True,
        "include_community_rank": True,
        "min_community_rank": 0,
        "community_rank_name": "rank",
        "include_community_weight": True,
        "community_weight_name": "occurrence weight",
        "normalize_community_weight": True,
        "max_tokens": 12_000, 
        "context_name": "Reports",
    }

    map_llm_params = {
        "max_tokens": 1000,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    reduce_llm_params = {
        "max_tokens": 2000, 
        "temperature": 0.0,
    }

    search_engine = GlobalSearch(
        model=model,
        context_builder=context_builder,
        token_encoder=token_encoder,
        max_data_tokens=12_000, 
        map_llm_params=map_llm_params,
        reduce_llm_params=reduce_llm_params,
        allow_general_knowledge=False, 
        json_mode=True, 
        context_builder_params=context_builder_params,
        concurrent_coroutines=32,
        response_type="multiple paragraphs", 
    )
    return search_engine
