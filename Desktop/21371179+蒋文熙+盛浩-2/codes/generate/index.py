import subprocess
import os
import asyncio
from dotenv import dotenv_values, set_key

def update_env_with_dotenv(file_path, key, value):
    set_key(file_path, key, value)

async def run_index(id: str):
    cur = os.curdir
    workdir = f'{cur}/temp/{id}'


    if os.path.isdir(os.path.join(workdir, 'output')):
        print('1111111111111111111111111111index finished!')
        return 

    process1 = await asyncio.create_subprocess_exec(
        'graphrag', 'init', '--root', workdir,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout1, stderr1 = await process1.communicate()
    if process1.returncode != 0:
        print(f'Error in init:\n{stderr1.decode()}')
        return

    # 同步设置环境变量
    update_env_with_dotenv(f'{workdir}/.env', "GRAPHRAG_API_KEY", os.environ['GRAPHRAG_API_KEY'])

    process2 = await asyncio.create_subprocess_exec(
        'graphrag', 'index', '--root', workdir,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout2, stderr2 = await process2.communicate()
    if process2.returncode != 0:
        print(f'Error in index:\n{stderr2.decode()}')
    
    print('index finished!')

