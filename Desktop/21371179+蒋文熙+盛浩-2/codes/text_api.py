import openai
api_key = ""


import asyncio
from generate import index, read

def file2txt(path:str):
    with open('path', 'r') as file:
        content = file.read()
    return content

def rough_summary():
    client = openai.OpenAI(api_key=api_key)
    background = file2txt('background.txt')
    method = file2txt('method.txt')
    experiment = file2txt('experiment.txt')
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": f"Based on the following information, generate a summary that enables users to quickly and accurately grasp the key content of the paper.\n The background and the inspiration of this paper can be summarized as {background}\n The research method proposed in this paper can be summarized as {method}\n The experiment and results of this paper can be summarized as {experiment}. Summary: "
            }
        ]
    )


    content = completion.choices[0].message.content
    return content
