import openai# 设置你的API密钥
import os

api_key = os.environ["GRAPHRAG_API_KEY"]


def translate_text(text, target_language='Chinese'):    
    client = openai.OpenAI(api_key=api_key)

    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": f"Translate the following text to {target_language} while keeping the original format: {text}"
            }
        ]
    )

    # print(completion.choices[0].message.content)
    return completion.choices[0].message.content

if __name__ == "__main__":    
    original_text = "Hello, how are you?"    
    target_lang = "Chinese"    
    translation = translate_text(original_text, target_lang)    
    print(f"Translated Text: {translation}")